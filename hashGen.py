#!/usr/bin/env python3
"""
Hash Generator - Un generator de hash-uri pentru parole și text
Suportă multiple algoritmi de hash: MD5, SHA1, SHA256, SHA512
"""

import hashlib
import getpass
import sys
from typing import Optional


class HashGenerator:
    """Clasa pentru generarea de hash-uri cu diferite algoritmi."""

    SUPPORTED_ALGORITHMS = {
        '1': ('MD5', hashlib.md5),
        '2': ('SHA1', hashlib.sha1),
        '3': ('SHA256', hashlib.sha256),
        '4': ('SHA512', hashlib.sha512)
    }

    def __init__(self):
        """Inițializează generatorul de hash-uri."""
        pass

    def generate_hash(self, text: str, algorithm: str) -> Optional[str]:
        """
        Generează hash-ul pentru textul dat folosind algoritmul specificat.

        Args:
            text (str): Textul pentru care se generează hash-ul
            algorithm (str): Algoritmul de hash ('1'-'4')

        Returns:
            Optional[str]: Hash-ul generat sau None în caz de eroare
        """
        try:
            if algorithm not in self.SUPPORTED_ALGORITHMS:
                print(f"❌ Algoritm invalid: {algorithm}")
                return None

            algorithm_name, hash_func = self.SUPPORTED_ALGORITHMS[algorithm]
            hash_object = hash_func(text.encode('utf-8'))
            hash_result = hash_object.hexdigest()

            return hash_result

        except Exception as e:
            print(f"❌ Eroare la generarea hash-ului: {e}")
            return None

    def display_menu(self):
        """Afișează meniul cu algoritmii disponibili."""
        print("\n" + "="*50)
        print("🔐 GENERATOR DE HASH-URI")
        print("="*50)
        print("Selectează algoritmul de hash:")

        for key, (name, _) in self.SUPPORTED_ALGORITHMS.items():
            print(f"  {key}. {name}")

        print("  0. Ieșire")
        print("="*50)

    def get_user_input(self) -> tuple[str, str]:
        """
        Obține input-ul de la utilizator (text și algoritm).

        Returns:
            tuple[str, str]: Textul și algoritmul selectat
        """
        try:
            # Obține textul de la utilizator (ascuns pentru parole)
            print("\n📝 Introdu textul/parola:")
            text = getpass.getpass("Text (ascuns): ")

            if not text.strip():
                print("⚠️  Textul nu poate fi gol!")
                return "", ""

            # Obține algoritmul
            algorithm = input("\n🔢 Selectează algoritmul (1-4): ").strip()

            return text, algorithm

        except KeyboardInterrupt:
            print("\n\n👋 Operațiune anulată de utilizator.")
            sys.exit(0)
        except Exception as e:
            print(f"❌ Eroare la citirea input-ului: {e}")
            return "", ""

    def display_result(self, text: str, algorithm: str, hash_result: str):
        """
        Afișează rezultatul hash-ului generat.

        Args:
            text (str): Textul original
            algorithm (str): Algoritmul folosit
            hash_result (str): Hash-ul generat
        """
        algorithm_name = self.SUPPORTED_ALGORITHMS[algorithm][0]

        print("\n" + "="*70)
        print("✅ REZULTAT")
        print("="*70)
        print(f"📄 Text original: {'*' * len(text)} ({len(text)} caractere)")
        print(f"🔐 Algoritm: {algorithm_name}")
        print(f"🔑 Hash generat: {hash_result}")
        print(f"📏 Lungime hash: {len(hash_result)} caractere")
        print("="*70)

    def run(self):
        """Rulează aplicația principală."""
        print("🚀 Bun venit la Hash Generator!")

        while True:
            try:
                self.display_menu()
                choice = input("\n➤ Alege o opțiune: ").strip()

                if choice == '0':
                    print("\n👋 La revedere!")
                    break

                if choice not in self.SUPPORTED_ALGORITHMS:
                    print("❌ Opțiune invalidă! Te rog să alegi 1-4 sau 0 pentru ieșire.")
                    continue

                text, algorithm = self.get_user_input()

                if not text or not algorithm:
                    continue

                if algorithm not in self.SUPPORTED_ALGORITHMS:
                    print("❌ Algoritm invalid! Te rog să alegi 1-4.")
                    continue

                hash_result = self.generate_hash(text, algorithm)

                if hash_result:
                    self.display_result(text, algorithm, hash_result)

                    # Întreabă dacă utilizatorul vrea să continue
                    continue_choice = input("\n🔄 Vrei să generezi alt hash? (d/n): ").strip().lower()
                    if continue_choice not in ['d', 'da', 'y', 'yes']:
                        print("\n👋 La revedere!")
                        break

            except KeyboardInterrupt:
                print("\n\n👋 Aplicația a fost închisă de utilizator.")
                break
            except Exception as e:
                print(f"❌ Eroare neașteptată: {e}")
                continue


def main():
    """Funcția principală a aplicației."""
    try:
        generator = HashGenerator()
        generator.run()
    except Exception as e:
        print(f"❌ Eroare critică: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
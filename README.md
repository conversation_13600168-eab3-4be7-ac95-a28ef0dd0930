# 🔐 Hash Generator

Un generator de hash-uri simplu și sigur pentru parole și text, scris în Python.

## ✨ Caracteristici

- **Multiple algoritmi de hash**: MD5, SHA1, SHA256, SHA512
- **Input securizat**: Parola este ascunsă în timpul introducerii
- **Interfață prietenoasă**: Meniu interactiv cu emoji-uri
- **Validare robustă**: Gestionarea erorilor și validarea input-ului
- **Cod curat**: Structurat cu clase și funcții
- **Documentație completă**: Comentarii și docstrings

## 🚀 Utilizare

### Rularea aplicației

```bash
python hashGen.py
```

### Exemplu de utilizare

```
🚀 Bun venit la Hash Generator!

==================================================
🔐 GENERATOR DE HASH-URI
==================================================
Selectează algoritmul de hash:
  1. MD5
  2. SHA1
  3. SHA256
  4. SHA512
  0. Ieșire
==================================================

➤ Alege o opțiune: 3

📝 Introdu textul/parola:
Text (ascuns): [parola ta aici]

🔢 Selectează algoritmul (1-4): 3

======================================================================
✅ REZULTAT
======================================================================
📄 Text original: ******** (8 caractere)
🔐 Algoritm: SHA256
🔑 Hash generat: e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
📏 Lungime hash: 64 caractere
======================================================================

🔄 Vrei să generezi alt hash? (d/n): n

👋 La revedere!
```

## 🛡️ Securitate

- **Input ascuns**: Parola nu este vizibilă în timpul introducerii
- **Fără stocare**: Parolele nu sunt salvate nicăieri
- **Algoritmi siguri**: Suportă algoritmi de hash criptografici standard

## 📋 Cerințe

- Python 3.6 sau mai nou
- Module standard Python (hashlib, getpass, sys, typing)

## 🔧 Îmbunătățiri față de versiunea originală

1. **Securitate îmbunătățită**: Folosește `getpass` pentru input ascuns
2. **Multiple algoritmi**: Nu doar SHA256, ci și MD5, SHA1, SHA512
3. **Interfață mai bună**: Meniu interactiv cu opțiuni clare
4. **Gestionarea erorilor**: Tratează excepțiile și validează input-ul
5. **Cod structurat**: Folosește clase și funcții pentru organizare
6. **Documentație**: Comentarii și docstrings complete
7. **Experiență utilizator**: Emoji-uri și mesaje clare

## 📝 Licență

Acest proiect este open source și disponibil sub licența MIT.
